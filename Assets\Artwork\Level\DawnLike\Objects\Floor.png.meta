fileFormatVersion: 2
guid: 52e9eaf7cf305a844865aae4b029f16d
timeCreated: **********
licenseType: Free
TextureImporter:
  fileIDToRecycleName:
    21300000: Floor_0
    21300002: Floor_1
    21300004: Floor_2
    21300006: Floor_3
    21300008: Floor_4
    21300010: Floor_5
    21300012: Floor_6
    21300014: Floor_7
    21300016: Floor_8
    21300018: Floor_9
    21300020: Floor_10
    21300022: Floor_11
    21300024: Floor_12
    21300026: Floor_13
    21300028: Floor_14
    21300030: Floor_15
    21300032: Floor_16
    21300034: Floor_17
    21300036: Floor_18
    21300038: Floor_19
    21300040: Floor_20
    21300042: Floor_21
    21300044: Floor_22
    21300046: Floor_23
    21300048: Floor_24
    21300050: Floor_25
    21300052: Floor_26
    21300054: Floor_27
    21300056: Floor_28
    21300058: Floor_29
    21300060: Floor_30
    21300062: Floor_31
    21300064: Floor_32
    21300066: Floor_33
    21300068: Floor_34
    21300070: Floor_35
    21300072: Floor_36
    21300074: Floor_37
    21300076: Floor_38
    21300078: Floor_39
    21300080: Floor_40
    21300082: Floor_41
    21300084: Floor_42
    21300086: Floor_43
    21300088: Floor_44
    21300090: Floor_45
    21300092: Floor_46
    21300094: Floor_47
    21300096: Floor_48
    21300098: Floor_49
    21300100: Floor_50
    21300102: Floor_51
    21300104: Floor_52
    21300106: Floor_53
    21300108: Floor_54
    21300110: Floor_55
    21300112: Floor_56
    21300114: Floor_57
    21300116: Floor_58
    21300118: Floor_59
    21300120: Floor_60
    21300122: Floor_61
    21300124: Floor_62
    21300126: Floor_63
    21300128: Floor_64
    21300130: Floor_65
    21300132: Floor_66
    21300134: Floor_67
    21300136: Floor_68
    21300138: Floor_69
    21300140: Floor_70
    21300142: Floor_71
    21300144: Floor_72
    21300146: Floor_73
    21300148: Floor_74
    21300150: Floor_75
    21300152: Floor_76
    21300154: Floor_77
    21300156: Floor_78
    21300158: Floor_79
    21300160: Floor_80
    21300162: Floor_81
    21300164: Floor_82
    21300166: Floor_83
    21300168: Floor_84
    21300170: Floor_85
    21300172: Floor_86
    21300174: Floor_87
    21300176: Floor_88
    21300178: Floor_89
    21300180: Floor_90
    21300182: Floor_91
    21300184: Floor_92
    21300186: Floor_93
    21300188: Floor_94
    21300190: Floor_95
    21300192: Floor_96
    21300194: Floor_97
    21300196: Floor_98
    21300198: Floor_99
    21300200: Floor_100
    21300202: Floor_101
    21300204: Floor_102
    21300206: Floor_103
    21300208: Floor_104
    21300210: Floor_105
    21300212: Floor_106
    21300214: Floor_107
    21300216: Floor_108
    21300218: Floor_109
    21300220: Floor_110
    21300222: Floor_111
    21300224: Floor_112
    21300226: Floor_113
    21300228: Floor_114
    21300230: Floor_115
    21300232: Floor_116
    21300234: Floor_117
    21300236: Floor_118
    21300238: Floor_119
    21300240: Floor_120
    21300242: Floor_121
    21300244: Floor_122
    21300246: Floor_123
    21300248: Floor_124
    21300250: Floor_125
    21300252: Floor_126
    21300254: Floor_127
    21300256: Floor_128
    21300258: Floor_129
    21300260: Floor_130
    21300262: Floor_131
    21300264: Floor_132
    21300266: Floor_133
    21300268: Floor_134
    21300270: Floor_135
    21300272: Floor_136
    21300274: Floor_137
    21300276: Floor_138
    21300278: Floor_139
    21300280: Floor_140
    21300282: Floor_141
    21300284: Floor_142
    21300286: Floor_143
    21300288: Floor_144
    21300290: Floor_145
    21300292: Floor_146
    21300294: Floor_147
    21300296: Floor_148
    21300298: Floor_149
    21300300: Floor_150
    21300302: Floor_151
    21300304: Floor_152
    21300306: Floor_153
    21300308: Floor_154
    21300310: Floor_155
    21300312: Floor_156
    21300314: Floor_157
    21300316: Floor_158
    21300318: Floor_159
    21300320: Floor_160
    21300322: Floor_161
    21300324: Floor_162
    21300326: Floor_163
    21300328: Floor_164
    21300330: Floor_165
    21300332: Floor_166
    21300334: Floor_167
    21300336: Floor_168
    21300338: Floor_169
    21300340: Floor_170
    21300342: Floor_171
    21300344: Floor_172
    21300346: Floor_173
    21300348: Floor_174
    21300350: Floor_175
    21300352: Floor_176
    21300354: Floor_177
    21300356: Floor_178
    21300358: Floor_179
    21300360: Floor_180
    21300362: Floor_181
    21300364: Floor_182
    21300366: Floor_183
    21300368: Floor_184
    21300370: Floor_185
    21300372: Floor_186
    21300374: Floor_187
    21300376: Floor_188
    21300378: Floor_189
    21300380: Floor_190
    21300382: Floor_191
    21300384: Floor_192
    21300386: Floor_193
    21300388: Floor_194
    21300390: Floor_195
    21300392: Floor_196
    21300394: Floor_197
    21300396: Floor_198
    21300398: Floor_199
    21300400: Floor_200
    21300402: Floor_201
    21300404: Floor_202
    21300406: Floor_203
    21300408: Floor_204
    21300410: Floor_205
    21300412: Floor_206
    21300414: Floor_207
    21300416: Floor_208
    21300418: Floor_209
    21300420: Floor_210
    21300422: Floor_211
    21300424: Floor_212
    21300426: Floor_213
    21300428: Floor_214
    21300430: Floor_215
    21300432: Floor_216
    21300434: Floor_217
    21300436: Floor_218
    21300438: Floor_219
    21300440: Floor_220
    21300442: Floor_221
    21300444: Floor_222
    21300446: Floor_223
    21300448: Floor_224
    21300450: Floor_225
    21300452: Floor_226
    21300454: Floor_227
    21300456: Floor_228
    21300458: Floor_229
    21300460: Floor_230
    21300462: Floor_231
    21300464: Floor_232
    21300466: Floor_233
    21300468: Floor_234
    21300470: Floor_235
    21300472: Floor_236
    21300474: Floor_237
    21300476: Floor_238
    21300478: Floor_239
    21300480: Floor_240
    21300482: Floor_241
    21300484: Floor_242
    21300486: Floor_243
    21300488: Floor_244
    21300490: Floor_245
    21300492: Floor_246
    21300494: Floor_247
    21300496: Floor_248
    21300498: Floor_249
    21300500: Floor_250
    21300502: Floor_251
    21300504: Floor_252
    21300506: Floor_253
    21300508: Floor_254
    21300510: Floor_255
    21300512: Floor_256
    21300514: Floor_257
    21300516: Floor_258
    21300518: Floor_259
    21300520: Floor_260
    21300522: Floor_261
    21300524: Floor_262
    21300526: Floor_263
    21300528: Floor_264
    21300530: Floor_265
    21300532: Floor_266
    21300534: Floor_267
    21300536: Floor_268
    21300538: Floor_269
    21300540: Floor_270
    21300542: Floor_271
    21300544: Floor_272
    21300546: Floor_273
    21300548: Floor_274
    21300550: Floor_275
    21300552: Floor_276
    21300554: Floor_277
    21300556: Floor_278
    21300558: Floor_279
    21300560: Floor_280
    21300562: Floor_281
    21300564: Floor_282
    21300566: Floor_283
    21300568: Floor_284
    21300570: Floor_285
    21300572: Floor_286
    21300574: Floor_287
    21300576: Floor_288
    21300578: Floor_289
    21300580: Floor_290
    21300582: Floor_291
    21300584: Floor_292
    21300586: Floor_293
    21300588: Floor_294
    21300590: Floor_295
    21300592: Floor_296
    21300594: Floor_297
    21300596: Floor_298
    21300598: Floor_299
    21300600: Floor_300
    21300602: Floor_301
    21300604: Floor_302
    21300606: Floor_303
    21300608: Floor_304
    21300610: Floor_305
    21300612: Floor_306
    21300614: Floor_307
    21300616: Floor_308
    21300618: Floor_309
    21300620: Floor_310
    21300622: Floor_311
    21300624: Floor_312
    21300626: Floor_313
    21300628: Floor_314
    21300630: Floor_315
    21300632: Floor_316
    21300634: Floor_317
    21300636: Floor_318
    21300638: Floor_319
    21300640: Floor_320
    21300642: Floor_321
    21300644: Floor_322
    21300646: Floor_323
    21300648: Floor_324
    21300650: Floor_325
    21300652: Floor_326
    21300654: Floor_327
    21300656: Floor_328
    21300658: Floor_329
    21300660: Floor_330
    21300662: Floor_331
    21300664: Floor_332
    21300666: Floor_333
    21300668: Floor_334
    21300670: Floor_335
    21300672: Floor_336
    21300674: Floor_337
    21300676: Floor_338
    21300678: Floor_339
    21300680: Floor_340
    21300682: Floor_341
    21300684: Floor_342
    21300686: Floor_343
    21300688: Floor_344
    21300690: Floor_345
    21300692: Floor_346
    21300694: Floor_347
    21300696: Floor_348
    21300698: Floor_349
    21300700: Floor_350
    21300702: Floor_351
    21300704: Floor_352
    21300706: Floor_353
    21300708: Floor_354
    21300710: Floor_355
    21300712: Floor_356
    21300714: Floor_357
    21300716: Floor_358
    21300718: Floor_359
    21300720: Floor_360
    21300722: Floor_361
    21300724: Floor_362
    21300726: Floor_363
    21300728: Floor_364
    21300730: Floor_365
    21300732: Floor_366
    21300734: Floor_367
    21300736: Floor_368
    21300738: Floor_369
    21300740: Floor_370
    21300742: Floor_371
    21300744: Floor_372
    21300746: Floor_373
    21300748: Floor_374
    21300750: Floor_375
    21300752: Floor_376
    21300754: Floor_377
    21300756: Floor_378
    21300758: Floor_379
    21300760: Floor_380
    21300762: Floor_381
    21300764: Floor_382
    21300766: Floor_383
    21300768: Floor_384
    21300770: Floor_385
    21300772: Floor_386
    21300774: Floor_387
    21300776: Floor_388
    21300778: Floor_389
    21300780: Floor_390
    21300782: Floor_391
    21300784: Floor_392
    21300786: Floor_393
    21300788: Floor_394
    21300790: Floor_395
    21300792: Floor_396
    21300794: Floor_397
    21300796: Floor_398
    21300798: Floor_399
    21300800: Floor_400
    21300802: Floor_401
    21300804: Floor_402
    21300806: Floor_403
    21300808: Floor_404
    21300810: Floor_405
    21300812: Floor_406
    21300814: Floor_407
    21300816: Floor_408
    21300818: Floor_409
    21300820: Floor_410
    21300822: Floor_411
    21300824: Floor_412
    21300826: Floor_413
    21300828: Floor_414
    21300830: Floor_415
    21300832: Floor_416
    21300834: Floor_417
    21300836: Floor_418
    21300838: Floor_419
    21300840: Floor_420
    21300842: Floor_421
    21300844: Floor_422
    21300846: Floor_423
    21300848: Floor_424
    21300850: Floor_425
    21300852: Floor_426
    21300854: Floor_427
    21300856: Floor_428
    21300858: Floor_429
    21300860: Floor_430
    21300862: Floor_431
    21300864: Floor_432
    21300866: Floor_433
    21300868: Floor_434
    21300870: Floor_435
    21300872: Floor_436
    21300874: Floor_437
    21300876: Floor_438
    21300878: Floor_439
    21300880: Floor_440
    21300882: Floor_441
    21300884: Floor_442
    21300886: Floor_443
    21300888: Floor_444
    21300890: Floor_445
    21300892: Floor_446
    21300894: Floor_447
    21300896: Floor_448
    21300898: Floor_449
    21300900: Floor_450
    21300902: Floor_451
    21300904: Floor_452
    21300906: Floor_453
    21300908: Floor_454
    21300910: Floor_455
    21300912: Floor_456
    21300914: Floor_457
    21300916: Floor_458
    21300918: Floor_459
    21300920: Floor_460
    21300922: Floor_461
    21300924: Floor_462
    21300926: Floor_463
    21300928: Floor_464
    21300930: Floor_465
    21300932: Floor_466
    21300934: Floor_467
    21300936: Floor_468
    21300938: Floor_469
    21300940: Floor_470
    21300942: Floor_471
    21300944: Floor_472
    21300946: Floor_473
    21300948: Floor_474
    21300950: Floor_475
    21300952: Floor_476
    21300954: Floor_477
    21300956: Floor_478
    21300958: Floor_479
  externalObjects: {}
  serializedVersion: 4
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 1
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: -1
    mipBias: -1
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spritePixelsToUnits: 98
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  platformSettings:
  - buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Floor_25
      rect:
        serializedVersion: 2
        x: 240
        y: 592
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_26
      rect:
        serializedVersion: 2
        x: 256
        y: 592
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_32
      rect:
        serializedVersion: 2
        x: 0
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_33
      rect:
        serializedVersion: 2
        x: 16
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_34
      rect:
        serializedVersion: 2
        x: 32
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_35
      rect:
        serializedVersion: 2
        x: 48
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_36
      rect:
        serializedVersion: 2
        x: 80
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_37
      rect:
        serializedVersion: 2
        x: 112
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_38
      rect:
        serializedVersion: 2
        x: 128
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_39
      rect:
        serializedVersion: 2
        x: 144
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_40
      rect:
        serializedVersion: 2
        x: 160
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_41
      rect:
        serializedVersion: 2
        x: 192
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_42
      rect:
        serializedVersion: 2
        x: 224
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_43
      rect:
        serializedVersion: 2
        x: 240
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_44
      rect:
        serializedVersion: 2
        x: 256
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_45
      rect:
        serializedVersion: 2
        x: 272
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_46
      rect:
        serializedVersion: 2
        x: 304
        y: 560
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_47
      rect:
        serializedVersion: 2
        x: 0
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_48
      rect:
        serializedVersion: 2
        x: 16
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_49
      rect:
        serializedVersion: 2
        x: 32
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_50
      rect:
        serializedVersion: 2
        x: 48
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_51
      rect:
        serializedVersion: 2
        x: 64
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_52
      rect:
        serializedVersion: 2
        x: 80
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_53
      rect:
        serializedVersion: 2
        x: 96
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_54
      rect:
        serializedVersion: 2
        x: 112
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_55
      rect:
        serializedVersion: 2
        x: 128
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_56
      rect:
        serializedVersion: 2
        x: 144
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_57
      rect:
        serializedVersion: 2
        x: 160
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_58
      rect:
        serializedVersion: 2
        x: 176
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_59
      rect:
        serializedVersion: 2
        x: 192
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_60
      rect:
        serializedVersion: 2
        x: 208
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_61
      rect:
        serializedVersion: 2
        x: 224
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_62
      rect:
        serializedVersion: 2
        x: 240
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_63
      rect:
        serializedVersion: 2
        x: 256
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_64
      rect:
        serializedVersion: 2
        x: 272
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_65
      rect:
        serializedVersion: 2
        x: 288
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_66
      rect:
        serializedVersion: 2
        x: 304
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_67
      rect:
        serializedVersion: 2
        x: 320
        y: 544
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_68
      rect:
        serializedVersion: 2
        x: 0
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_69
      rect:
        serializedVersion: 2
        x: 16
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_70
      rect:
        serializedVersion: 2
        x: 32
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_71
      rect:
        serializedVersion: 2
        x: 48
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_72
      rect:
        serializedVersion: 2
        x: 112
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_73
      rect:
        serializedVersion: 2
        x: 128
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_74
      rect:
        serializedVersion: 2
        x: 144
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_75
      rect:
        serializedVersion: 2
        x: 160
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_76
      rect:
        serializedVersion: 2
        x: 224
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_77
      rect:
        serializedVersion: 2
        x: 240
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_78
      rect:
        serializedVersion: 2
        x: 256
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_79
      rect:
        serializedVersion: 2
        x: 272
        y: 528
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_80
      rect:
        serializedVersion: 2
        x: 0
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_81
      rect:
        serializedVersion: 2
        x: 16
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_82
      rect:
        serializedVersion: 2
        x: 32
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_83
      rect:
        serializedVersion: 2
        x: 48
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_84
      rect:
        serializedVersion: 2
        x: 80
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_85
      rect:
        serializedVersion: 2
        x: 112
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_86
      rect:
        serializedVersion: 2
        x: 128
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_87
      rect:
        serializedVersion: 2
        x: 144
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_88
      rect:
        serializedVersion: 2
        x: 160
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_89
      rect:
        serializedVersion: 2
        x: 192
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_90
      rect:
        serializedVersion: 2
        x: 224
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_91
      rect:
        serializedVersion: 2
        x: 240
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_92
      rect:
        serializedVersion: 2
        x: 256
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_93
      rect:
        serializedVersion: 2
        x: 272
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_94
      rect:
        serializedVersion: 2
        x: 304
        y: 512
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_95
      rect:
        serializedVersion: 2
        x: 0
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_96
      rect:
        serializedVersion: 2
        x: 16
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_97
      rect:
        serializedVersion: 2
        x: 32
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_98
      rect:
        serializedVersion: 2
        x: 48
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_99
      rect:
        serializedVersion: 2
        x: 64
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_100
      rect:
        serializedVersion: 2
        x: 80
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_101
      rect:
        serializedVersion: 2
        x: 96
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_102
      rect:
        serializedVersion: 2
        x: 112
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_103
      rect:
        serializedVersion: 2
        x: 128
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_104
      rect:
        serializedVersion: 2
        x: 144
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_105
      rect:
        serializedVersion: 2
        x: 160
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_106
      rect:
        serializedVersion: 2
        x: 176
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_107
      rect:
        serializedVersion: 2
        x: 192
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_108
      rect:
        serializedVersion: 2
        x: 208
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_109
      rect:
        serializedVersion: 2
        x: 224
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_110
      rect:
        serializedVersion: 2
        x: 240
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_111
      rect:
        serializedVersion: 2
        x: 256
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_112
      rect:
        serializedVersion: 2
        x: 272
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_113
      rect:
        serializedVersion: 2
        x: 288
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_114
      rect:
        serializedVersion: 2
        x: 304
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_115
      rect:
        serializedVersion: 2
        x: 320
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_116
      rect:
        serializedVersion: 2
        x: 0
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_117
      rect:
        serializedVersion: 2
        x: 16
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_118
      rect:
        serializedVersion: 2
        x: 32
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_119
      rect:
        serializedVersion: 2
        x: 48
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_120
      rect:
        serializedVersion: 2
        x: 112
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_121
      rect:
        serializedVersion: 2
        x: 128
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_122
      rect:
        serializedVersion: 2
        x: 144
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_123
      rect:
        serializedVersion: 2
        x: 160
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_124
      rect:
        serializedVersion: 2
        x: 224
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_125
      rect:
        serializedVersion: 2
        x: 240
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_126
      rect:
        serializedVersion: 2
        x: 256
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_127
      rect:
        serializedVersion: 2
        x: 272
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_128
      rect:
        serializedVersion: 2
        x: 0
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_129
      rect:
        serializedVersion: 2
        x: 16
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_130
      rect:
        serializedVersion: 2
        x: 32
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_131
      rect:
        serializedVersion: 2
        x: 48
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_132
      rect:
        serializedVersion: 2
        x: 80
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_133
      rect:
        serializedVersion: 2
        x: 112
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_134
      rect:
        serializedVersion: 2
        x: 128
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_135
      rect:
        serializedVersion: 2
        x: 144
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_136
      rect:
        serializedVersion: 2
        x: 160
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_137
      rect:
        serializedVersion: 2
        x: 192
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_138
      rect:
        serializedVersion: 2
        x: 224
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_139
      rect:
        serializedVersion: 2
        x: 240
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_140
      rect:
        serializedVersion: 2
        x: 256
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_141
      rect:
        serializedVersion: 2
        x: 272
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_142
      rect:
        serializedVersion: 2
        x: 304
        y: 464
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_143
      rect:
        serializedVersion: 2
        x: 0
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_144
      rect:
        serializedVersion: 2
        x: 16
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_145
      rect:
        serializedVersion: 2
        x: 32
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_146
      rect:
        serializedVersion: 2
        x: 48
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_147
      rect:
        serializedVersion: 2
        x: 64
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_148
      rect:
        serializedVersion: 2
        x: 80
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_149
      rect:
        serializedVersion: 2
        x: 96
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_150
      rect:
        serializedVersion: 2
        x: 112
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_151
      rect:
        serializedVersion: 2
        x: 128
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_152
      rect:
        serializedVersion: 2
        x: 144
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_153
      rect:
        serializedVersion: 2
        x: 160
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_154
      rect:
        serializedVersion: 2
        x: 176
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_155
      rect:
        serializedVersion: 2
        x: 192
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_156
      rect:
        serializedVersion: 2
        x: 208
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_157
      rect:
        serializedVersion: 2
        x: 224
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_158
      rect:
        serializedVersion: 2
        x: 240
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_159
      rect:
        serializedVersion: 2
        x: 256
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_160
      rect:
        serializedVersion: 2
        x: 272
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_161
      rect:
        serializedVersion: 2
        x: 288
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_162
      rect:
        serializedVersion: 2
        x: 304
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_163
      rect:
        serializedVersion: 2
        x: 320
        y: 448
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_164
      rect:
        serializedVersion: 2
        x: 0
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_165
      rect:
        serializedVersion: 2
        x: 16
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_166
      rect:
        serializedVersion: 2
        x: 32
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_167
      rect:
        serializedVersion: 2
        x: 48
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_168
      rect:
        serializedVersion: 2
        x: 112
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_169
      rect:
        serializedVersion: 2
        x: 128
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_170
      rect:
        serializedVersion: 2
        x: 144
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_171
      rect:
        serializedVersion: 2
        x: 160
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_172
      rect:
        serializedVersion: 2
        x: 224
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_173
      rect:
        serializedVersion: 2
        x: 240
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_174
      rect:
        serializedVersion: 2
        x: 256
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_175
      rect:
        serializedVersion: 2
        x: 272
        y: 432
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_176
      rect:
        serializedVersion: 2
        x: 0
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_177
      rect:
        serializedVersion: 2
        x: 16
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_178
      rect:
        serializedVersion: 2
        x: 32
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_179
      rect:
        serializedVersion: 2
        x: 48
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_180
      rect:
        serializedVersion: 2
        x: 80
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_181
      rect:
        serializedVersion: 2
        x: 112
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_182
      rect:
        serializedVersion: 2
        x: 128
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_183
      rect:
        serializedVersion: 2
        x: 144
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_184
      rect:
        serializedVersion: 2
        x: 160
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_185
      rect:
        serializedVersion: 2
        x: 192
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_186
      rect:
        serializedVersion: 2
        x: 224
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_187
      rect:
        serializedVersion: 2
        x: 240
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_188
      rect:
        serializedVersion: 2
        x: 256
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_189
      rect:
        serializedVersion: 2
        x: 272
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_190
      rect:
        serializedVersion: 2
        x: 304
        y: 416
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_191
      rect:
        serializedVersion: 2
        x: 0
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_192
      rect:
        serializedVersion: 2
        x: 16
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_193
      rect:
        serializedVersion: 2
        x: 32
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_194
      rect:
        serializedVersion: 2
        x: 48
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_195
      rect:
        serializedVersion: 2
        x: 64
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_196
      rect:
        serializedVersion: 2
        x: 80
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_197
      rect:
        serializedVersion: 2
        x: 96
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_198
      rect:
        serializedVersion: 2
        x: 112
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_199
      rect:
        serializedVersion: 2
        x: 128
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_200
      rect:
        serializedVersion: 2
        x: 144
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_201
      rect:
        serializedVersion: 2
        x: 160
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_202
      rect:
        serializedVersion: 2
        x: 176
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_203
      rect:
        serializedVersion: 2
        x: 192
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_204
      rect:
        serializedVersion: 2
        x: 208
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_205
      rect:
        serializedVersion: 2
        x: 224
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_206
      rect:
        serializedVersion: 2
        x: 240
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_207
      rect:
        serializedVersion: 2
        x: 256
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_208
      rect:
        serializedVersion: 2
        x: 272
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_209
      rect:
        serializedVersion: 2
        x: 288
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_210
      rect:
        serializedVersion: 2
        x: 304
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_211
      rect:
        serializedVersion: 2
        x: 320
        y: 400
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_212
      rect:
        serializedVersion: 2
        x: 0
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_213
      rect:
        serializedVersion: 2
        x: 16
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_214
      rect:
        serializedVersion: 2
        x: 32
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_215
      rect:
        serializedVersion: 2
        x: 48
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_216
      rect:
        serializedVersion: 2
        x: 112
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_217
      rect:
        serializedVersion: 2
        x: 128
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_218
      rect:
        serializedVersion: 2
        x: 144
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_219
      rect:
        serializedVersion: 2
        x: 160
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_220
      rect:
        serializedVersion: 2
        x: 224
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_221
      rect:
        serializedVersion: 2
        x: 240
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_222
      rect:
        serializedVersion: 2
        x: 256
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_223
      rect:
        serializedVersion: 2
        x: 272
        y: 384
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_224
      rect:
        serializedVersion: 2
        x: 0
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_225
      rect:
        serializedVersion: 2
        x: 16
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_226
      rect:
        serializedVersion: 2
        x: 32
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_227
      rect:
        serializedVersion: 2
        x: 48
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_228
      rect:
        serializedVersion: 2
        x: 80
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_229
      rect:
        serializedVersion: 2
        x: 112
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_230
      rect:
        serializedVersion: 2
        x: 128
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_231
      rect:
        serializedVersion: 2
        x: 144
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_232
      rect:
        serializedVersion: 2
        x: 160
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_233
      rect:
        serializedVersion: 2
        x: 192
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_234
      rect:
        serializedVersion: 2
        x: 224
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_235
      rect:
        serializedVersion: 2
        x: 240
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_236
      rect:
        serializedVersion: 2
        x: 256
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_237
      rect:
        serializedVersion: 2
        x: 272
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_238
      rect:
        serializedVersion: 2
        x: 304
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_239
      rect:
        serializedVersion: 2
        x: 0
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_240
      rect:
        serializedVersion: 2
        x: 16
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_241
      rect:
        serializedVersion: 2
        x: 32
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_242
      rect:
        serializedVersion: 2
        x: 48
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_243
      rect:
        serializedVersion: 2
        x: 64
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_244
      rect:
        serializedVersion: 2
        x: 80
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_245
      rect:
        serializedVersion: 2
        x: 96
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_246
      rect:
        serializedVersion: 2
        x: 112
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_247
      rect:
        serializedVersion: 2
        x: 128
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_248
      rect:
        serializedVersion: 2
        x: 144
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_249
      rect:
        serializedVersion: 2
        x: 160
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_250
      rect:
        serializedVersion: 2
        x: 176
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_251
      rect:
        serializedVersion: 2
        x: 192
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_252
      rect:
        serializedVersion: 2
        x: 208
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_253
      rect:
        serializedVersion: 2
        x: 224
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_254
      rect:
        serializedVersion: 2
        x: 240
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_255
      rect:
        serializedVersion: 2
        x: 256
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_256
      rect:
        serializedVersion: 2
        x: 272
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_257
      rect:
        serializedVersion: 2
        x: 288
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_258
      rect:
        serializedVersion: 2
        x: 304
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_259
      rect:
        serializedVersion: 2
        x: 320
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_260
      rect:
        serializedVersion: 2
        x: 0
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_261
      rect:
        serializedVersion: 2
        x: 16
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_262
      rect:
        serializedVersion: 2
        x: 32
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_263
      rect:
        serializedVersion: 2
        x: 48
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_264
      rect:
        serializedVersion: 2
        x: 112
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_265
      rect:
        serializedVersion: 2
        x: 128
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_266
      rect:
        serializedVersion: 2
        x: 144
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_267
      rect:
        serializedVersion: 2
        x: 160
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_268
      rect:
        serializedVersion: 2
        x: 224
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_269
      rect:
        serializedVersion: 2
        x: 240
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_270
      rect:
        serializedVersion: 2
        x: 256
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_271
      rect:
        serializedVersion: 2
        x: 272
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_272
      rect:
        serializedVersion: 2
        x: 0
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_273
      rect:
        serializedVersion: 2
        x: 16
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_274
      rect:
        serializedVersion: 2
        x: 32
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_275
      rect:
        serializedVersion: 2
        x: 48
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_276
      rect:
        serializedVersion: 2
        x: 80
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_277
      rect:
        serializedVersion: 2
        x: 112
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_278
      rect:
        serializedVersion: 2
        x: 128
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_279
      rect:
        serializedVersion: 2
        x: 144
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_280
      rect:
        serializedVersion: 2
        x: 160
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_281
      rect:
        serializedVersion: 2
        x: 192
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_282
      rect:
        serializedVersion: 2
        x: 224
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_283
      rect:
        serializedVersion: 2
        x: 240
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_284
      rect:
        serializedVersion: 2
        x: 256
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_285
      rect:
        serializedVersion: 2
        x: 272
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_286
      rect:
        serializedVersion: 2
        x: 304
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_287
      rect:
        serializedVersion: 2
        x: 0
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_288
      rect:
        serializedVersion: 2
        x: 16
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_289
      rect:
        serializedVersion: 2
        x: 32
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_290
      rect:
        serializedVersion: 2
        x: 48
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_291
      rect:
        serializedVersion: 2
        x: 64
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_292
      rect:
        serializedVersion: 2
        x: 80
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_293
      rect:
        serializedVersion: 2
        x: 96
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_294
      rect:
        serializedVersion: 2
        x: 112
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_295
      rect:
        serializedVersion: 2
        x: 128
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_296
      rect:
        serializedVersion: 2
        x: 144
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_297
      rect:
        serializedVersion: 2
        x: 160
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_298
      rect:
        serializedVersion: 2
        x: 176
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_299
      rect:
        serializedVersion: 2
        x: 192
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_300
      rect:
        serializedVersion: 2
        x: 208
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_301
      rect:
        serializedVersion: 2
        x: 224
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_302
      rect:
        serializedVersion: 2
        x: 240
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_303
      rect:
        serializedVersion: 2
        x: 256
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_304
      rect:
        serializedVersion: 2
        x: 272
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_305
      rect:
        serializedVersion: 2
        x: 288
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_306
      rect:
        serializedVersion: 2
        x: 304
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_307
      rect:
        serializedVersion: 2
        x: 320
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_308
      rect:
        serializedVersion: 2
        x: 0
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_309
      rect:
        serializedVersion: 2
        x: 16
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_310
      rect:
        serializedVersion: 2
        x: 32
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_311
      rect:
        serializedVersion: 2
        x: 48
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_312
      rect:
        serializedVersion: 2
        x: 112
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_313
      rect:
        serializedVersion: 2
        x: 128
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_314
      rect:
        serializedVersion: 2
        x: 144
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_315
      rect:
        serializedVersion: 2
        x: 160
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_316
      rect:
        serializedVersion: 2
        x: 224
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_317
      rect:
        serializedVersion: 2
        x: 240
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_318
      rect:
        serializedVersion: 2
        x: 256
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_319
      rect:
        serializedVersion: 2
        x: 272
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_320
      rect:
        serializedVersion: 2
        x: 0
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_321
      rect:
        serializedVersion: 2
        x: 16
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_322
      rect:
        serializedVersion: 2
        x: 32
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_323
      rect:
        serializedVersion: 2
        x: 48
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_324
      rect:
        serializedVersion: 2
        x: 80
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_325
      rect:
        serializedVersion: 2
        x: 112
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_326
      rect:
        serializedVersion: 2
        x: 128
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_327
      rect:
        serializedVersion: 2
        x: 144
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_328
      rect:
        serializedVersion: 2
        x: 160
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_329
      rect:
        serializedVersion: 2
        x: 192
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_330
      rect:
        serializedVersion: 2
        x: 224
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_331
      rect:
        serializedVersion: 2
        x: 240
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_332
      rect:
        serializedVersion: 2
        x: 256
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_333
      rect:
        serializedVersion: 2
        x: 272
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_334
      rect:
        serializedVersion: 2
        x: 304
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_335
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_336
      rect:
        serializedVersion: 2
        x: 16
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_337
      rect:
        serializedVersion: 2
        x: 32
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_338
      rect:
        serializedVersion: 2
        x: 48
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_339
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_340
      rect:
        serializedVersion: 2
        x: 80
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_341
      rect:
        serializedVersion: 2
        x: 96
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_342
      rect:
        serializedVersion: 2
        x: 112
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_343
      rect:
        serializedVersion: 2
        x: 128
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_344
      rect:
        serializedVersion: 2
        x: 144
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_345
      rect:
        serializedVersion: 2
        x: 160
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_346
      rect:
        serializedVersion: 2
        x: 176
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_347
      rect:
        serializedVersion: 2
        x: 192
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_348
      rect:
        serializedVersion: 2
        x: 208
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_349
      rect:
        serializedVersion: 2
        x: 224
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_350
      rect:
        serializedVersion: 2
        x: 240
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_351
      rect:
        serializedVersion: 2
        x: 256
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_352
      rect:
        serializedVersion: 2
        x: 272
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_353
      rect:
        serializedVersion: 2
        x: 288
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_354
      rect:
        serializedVersion: 2
        x: 304
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_355
      rect:
        serializedVersion: 2
        x: 320
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_356
      rect:
        serializedVersion: 2
        x: 0
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_357
      rect:
        serializedVersion: 2
        x: 16
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_358
      rect:
        serializedVersion: 2
        x: 32
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_359
      rect:
        serializedVersion: 2
        x: 48
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_360
      rect:
        serializedVersion: 2
        x: 112
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_361
      rect:
        serializedVersion: 2
        x: 128
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_362
      rect:
        serializedVersion: 2
        x: 144
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_363
      rect:
        serializedVersion: 2
        x: 160
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_364
      rect:
        serializedVersion: 2
        x: 224
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_365
      rect:
        serializedVersion: 2
        x: 240
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_366
      rect:
        serializedVersion: 2
        x: 256
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_367
      rect:
        serializedVersion: 2
        x: 272
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_368
      rect:
        serializedVersion: 2
        x: 0
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_369
      rect:
        serializedVersion: 2
        x: 16
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_370
      rect:
        serializedVersion: 2
        x: 32
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_371
      rect:
        serializedVersion: 2
        x: 48
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_372
      rect:
        serializedVersion: 2
        x: 80
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_373
      rect:
        serializedVersion: 2
        x: 112
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_374
      rect:
        serializedVersion: 2
        x: 128
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_375
      rect:
        serializedVersion: 2
        x: 144
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_376
      rect:
        serializedVersion: 2
        x: 160
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_377
      rect:
        serializedVersion: 2
        x: 192
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_378
      rect:
        serializedVersion: 2
        x: 224
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_379
      rect:
        serializedVersion: 2
        x: 240
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_380
      rect:
        serializedVersion: 2
        x: 256
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_381
      rect:
        serializedVersion: 2
        x: 272
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_382
      rect:
        serializedVersion: 2
        x: 304
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_383
      rect:
        serializedVersion: 2
        x: 0
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_384
      rect:
        serializedVersion: 2
        x: 16
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_385
      rect:
        serializedVersion: 2
        x: 32
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_386
      rect:
        serializedVersion: 2
        x: 48
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_387
      rect:
        serializedVersion: 2
        x: 64
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_388
      rect:
        serializedVersion: 2
        x: 80
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_389
      rect:
        serializedVersion: 2
        x: 96
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_390
      rect:
        serializedVersion: 2
        x: 112
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_391
      rect:
        serializedVersion: 2
        x: 128
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_392
      rect:
        serializedVersion: 2
        x: 144
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_393
      rect:
        serializedVersion: 2
        x: 160
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_394
      rect:
        serializedVersion: 2
        x: 176
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_395
      rect:
        serializedVersion: 2
        x: 192
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_396
      rect:
        serializedVersion: 2
        x: 208
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_397
      rect:
        serializedVersion: 2
        x: 224
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_398
      rect:
        serializedVersion: 2
        x: 240
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_399
      rect:
        serializedVersion: 2
        x: 256
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_400
      rect:
        serializedVersion: 2
        x: 272
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_401
      rect:
        serializedVersion: 2
        x: 288
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_402
      rect:
        serializedVersion: 2
        x: 304
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_403
      rect:
        serializedVersion: 2
        x: 320
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_404
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_405
      rect:
        serializedVersion: 2
        x: 16
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_406
      rect:
        serializedVersion: 2
        x: 32
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_407
      rect:
        serializedVersion: 2
        x: 48
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_408
      rect:
        serializedVersion: 2
        x: 112
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_409
      rect:
        serializedVersion: 2
        x: 128
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_410
      rect:
        serializedVersion: 2
        x: 144
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_411
      rect:
        serializedVersion: 2
        x: 160
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_412
      rect:
        serializedVersion: 2
        x: 224
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_413
      rect:
        serializedVersion: 2
        x: 240
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_414
      rect:
        serializedVersion: 2
        x: 256
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_415
      rect:
        serializedVersion: 2
        x: 272
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_416
      rect:
        serializedVersion: 2
        x: 0
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_417
      rect:
        serializedVersion: 2
        x: 16
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_418
      rect:
        serializedVersion: 2
        x: 32
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_419
      rect:
        serializedVersion: 2
        x: 48
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_420
      rect:
        serializedVersion: 2
        x: 80
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_421
      rect:
        serializedVersion: 2
        x: 0
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_422
      rect:
        serializedVersion: 2
        x: 16
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_423
      rect:
        serializedVersion: 2
        x: 32
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_424
      rect:
        serializedVersion: 2
        x: 48
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_425
      rect:
        serializedVersion: 2
        x: 64
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_426
      rect:
        serializedVersion: 2
        x: 80
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_427
      rect:
        serializedVersion: 2
        x: 96
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_428
      rect:
        serializedVersion: 2
        x: 0
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_429
      rect:
        serializedVersion: 2
        x: 16
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_430
      rect:
        serializedVersion: 2
        x: 32
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_431
      rect:
        serializedVersion: 2
        x: 48
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_432
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_433
      rect:
        serializedVersion: 2
        x: 16
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_434
      rect:
        serializedVersion: 2
        x: 32
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_435
      rect:
        serializedVersion: 2
        x: 48
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_436
      rect:
        serializedVersion: 2
        x: 80
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_437
      rect:
        serializedVersion: 2
        x: 0
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_438
      rect:
        serializedVersion: 2
        x: 16
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_439
      rect:
        serializedVersion: 2
        x: 32
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_440
      rect:
        serializedVersion: 2
        x: 48
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_441
      rect:
        serializedVersion: 2
        x: 64
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_442
      rect:
        serializedVersion: 2
        x: 80
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_443
      rect:
        serializedVersion: 2
        x: 96
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_444
      rect:
        serializedVersion: 2
        x: 0
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_445
      rect:
        serializedVersion: 2
        x: 16
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_446
      rect:
        serializedVersion: 2
        x: 32
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_447
      rect:
        serializedVersion: 2
        x: 48
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_448
      rect:
        serializedVersion: 2
        x: 0
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_449
      rect:
        serializedVersion: 2
        x: 16
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_450
      rect:
        serializedVersion: 2
        x: 32
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_451
      rect:
        serializedVersion: 2
        x: 48
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_452
      rect:
        serializedVersion: 2
        x: 80
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_453
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_454
      rect:
        serializedVersion: 2
        x: 16
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_455
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_456
      rect:
        serializedVersion: 2
        x: 48
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_457
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_458
      rect:
        serializedVersion: 2
        x: 80
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_459
      rect:
        serializedVersion: 2
        x: 96
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_460
      rect:
        serializedVersion: 2
        x: 0
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_461
      rect:
        serializedVersion: 2
        x: 16
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_462
      rect:
        serializedVersion: 2
        x: 32
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_463
      rect:
        serializedVersion: 2
        x: 48
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_464
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_465
      rect:
        serializedVersion: 2
        x: 16
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_466
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_467
      rect:
        serializedVersion: 2
        x: 48
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_468
      rect:
        serializedVersion: 2
        x: 80
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_469
      rect:
        serializedVersion: 2
        x: 0
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_470
      rect:
        serializedVersion: 2
        x: 16
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_471
      rect:
        serializedVersion: 2
        x: 32
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_472
      rect:
        serializedVersion: 2
        x: 48
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_473
      rect:
        serializedVersion: 2
        x: 64
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_474
      rect:
        serializedVersion: 2
        x: 80
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_475
      rect:
        serializedVersion: 2
        x: 96
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_476
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_477
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_478
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Floor_479
      rect:
        serializedVersion: 2
        x: 48
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    outline: []
    physicsShape: []
  spritePackingTag: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
