fileFormatVersion: 2
guid: f24cf792072ca3845b644a9411597cc9
timeCreated: **********
licenseType: Free
TextureImporter:
  fileIDToRecycleName:
    21300000: Pit0_0
    21300002: Pit0_1
    21300004: Pit0_2
    21300006: Pit0_3
    21300008: Pit0_4
    21300010: Pit0_5
    21300012: Pit0_6
    21300014: Pit0_7
    21300016: Pit0_8
    21300018: Pit0_9
    21300020: Pit0_10
    21300022: Pit0_11
    21300024: Pit0_12
    21300026: Pit0_13
    21300028: Pit0_14
    21300030: Pit0_15
    21300032: Pit0_16
    21300034: Pit0_17
    21300036: Pit0_18
    21300038: Pit0_19
    21300040: Pit0_20
    21300042: Pit0_21
    21300044: Pit0_22
    21300046: Pit0_23
    21300048: Pit0_24
    21300050: Pit0_25
    21300052: Pit0_26
    21300054: Pit0_27
    21300056: Pit0_28
    21300058: Pit0_29
    21300060: Pit0_30
    21300062: Pit0_31
    21300064: Pit0_32
    21300066: Pit0_33
    21300068: Pit0_34
    21300070: Pit0_35
    21300072: Pit0_36
    21300074: Pit0_37
    21300076: Pit0_38
    21300078: Pit0_39
    21300080: Pit0_40
    21300082: Pit0_41
    21300084: Pit0_42
    21300086: Pit0_43
    21300088: Pit0_44
    21300090: Pit0_45
    21300092: Pit0_46
    21300094: Pit0_47
    21300096: Pit0_48
    21300098: Pit0_49
    21300100: Pit0_50
    21300102: Pit0_51
    21300104: Pit0_52
    21300106: Pit0_53
    21300108: Pit0_54
    21300110: light_water_4
    21300112: light_water_0
    21300114: light_water_1
    21300116: Pit0_58
    21300118: Pit0_59
    21300120: Pit0_60
    21300122: Pit0_61
    21300124: light_water_5
    21300126: light_water_2
    21300128: light_water_3
    21300130: Pit0_65
    21300132: Pit0_66
    21300134: Pit0_67
    21300136: Pit0_68
    21300138: Pit0_69
    21300140: Pit0_70
    21300142: Pit0_71
    21300144: Pit0_72
    21300146: Pit0_73
    21300148: Pit0_74
    21300150: Pit0_75
    21300152: Pit0_76
    21300154: deep_water_4
    21300156: deep_water_0
    21300158: deep_water_1
    21300160: Pit0_80
    21300162: Pit0_81
    21300164: Pit0_82
    21300166: Pit0_83
    21300168: deep_water_5
    21300170: deep_water_2
    21300172: deep_water_3
    21300174: Pit0_87
    21300176: Pit0_88
    21300178: Pit0_89
    21300180: Pit0_90
    21300182: Pit0_91
    21300184: Pit0_92
    21300186: Pit0_93
    21300188: Pit0_94
    21300190: Pit0_95
    21300192: Pit0_96
    21300194: Pit0_97
    21300196: Pit0_98
    21300198: Pit0_99
    21300200: Pit0_100
    21300202: Pit0_101
    21300204: Pit0_102
    21300206: Pit0_103
    21300208: Pit0_104
    21300210: Pit0_105
    21300212: Pit0_106
    21300214: Pit0_107
    21300216: Pit0_108
    21300218: Pit0_109
    21300220: Pit0_110
    21300222: Pit0_111
    21300224: Pit0_112
    21300226: Pit0_113
    21300228: Pit0_114
    21300230: Pit0_115
    21300232: Pit0_116
    21300234: Pit0_117
    21300236: Pit0_118
    21300238: Pit0_119
    21300240: Pit0_120
    21300242: Pit0_121
    21300244: Pit0_122
    21300246: Pit0_123
    21300248: Pit0_124
    21300250: Pit0_125
    21300252: Pit0_126
    21300254: Pit0_127
    21300256: Pit0_128
    21300258: Pit0_129
    21300260: Pit0_130
    21300262: Pit0_131
    21300264: Pit0_132
    21300266: Pit0_133
    21300268: Pit0_134
    21300270: Pit0_135
    21300272: Pit0_136
    21300274: Pit0_137
    21300276: Pit0_138
    21300278: Pit0_139
    21300280: Pit0_140
    21300282: Pit0_141
    21300284: Pit0_142
    21300286: Lava_4
    21300288: Lava_0
    21300290: Lava_1
    21300292: Pit0_146
    21300294: Pit0_147
    21300296: Pit0_148
    21300298: Pit0_149
    21300300: Lava_5
    21300302: Lava_2
    21300304: Lava_2
    21300306: Pit0_153
    21300308: water_lava_4
    21300310: water_lava_0
    21300312: water_lava_1
    21300314: Pit0_157
    21300316: Pit0_158
    21300318: Pit0_159
    21300320: Pit0_160
    21300322: water_lava_5
    21300324: water_lava_2
    21300326: water_lava_3
    21300328: Pit0_164
    21300330: Pit0_165
    21300332: Pit0_166
    21300334: Pit0_167
    21300336: Pit0_168
    21300338: Pit0_169
    21300340: Pit0_170
    21300342: Pit0_171
    21300344: Pit0_172
    21300346: Pit0_173
    21300348: Pit0_174
    21300350: Pit0_175
    21300352: Lava_3
  externalObjects: {}
  serializedVersion: 4
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 1
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: -1
    mipBias: -1
    wrapU: 0
    wrapV: 0
    wrapW: 0
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spritePixelsToUnits: 90
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  platformSettings:
  - buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Pit0_3
      rect:
        serializedVersion: 2
        x: 64
        y: 496
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_10
      rect:
        serializedVersion: 2
        x: 64
        y: 480
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_44
      rect:
        serializedVersion: 2
        x: 0
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_45
      rect:
        serializedVersion: 2
        x: 16
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_46
      rect:
        serializedVersion: 2
        x: 32
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_47
      rect:
        serializedVersion: 2
        x: 64
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_48
      rect:
        serializedVersion: 2
        x: 80
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_49
      rect:
        serializedVersion: 2
        x: 96
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_50
      rect:
        serializedVersion: 2
        x: 112
        y: 368
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_51
      rect:
        serializedVersion: 2
        x: 0
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_52
      rect:
        serializedVersion: 2
        x: 16
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_53
      rect:
        serializedVersion: 2
        x: 32
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_54
      rect:
        serializedVersion: 2
        x: 64
        y: 352
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: light_water_4
      rect:
        serializedVersion: 2
        x: 0
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: light_water_0
      rect:
        serializedVersion: 2
        x: 16
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: light_water_1
      rect:
        serializedVersion: 2
        x: 32
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_58
      rect:
        serializedVersion: 2
        x: 64
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_59
      rect:
        serializedVersion: 2
        x: 80
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_60
      rect:
        serializedVersion: 2
        x: 96
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_61
      rect:
        serializedVersion: 2
        x: 112
        y: 336
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: light_water_5
      rect:
        serializedVersion: 2
        x: 0
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: light_water_2
      rect:
        serializedVersion: 2
        x: 16
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: light_water_3
      rect:
        serializedVersion: 2
        x: 32
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_65
      rect:
        serializedVersion: 2
        x: 64
        y: 320
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_66
      rect:
        serializedVersion: 2
        x: 0
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_67
      rect:
        serializedVersion: 2
        x: 16
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_68
      rect:
        serializedVersion: 2
        x: 32
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_69
      rect:
        serializedVersion: 2
        x: 64
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_70
      rect:
        serializedVersion: 2
        x: 80
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_71
      rect:
        serializedVersion: 2
        x: 96
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_72
      rect:
        serializedVersion: 2
        x: 112
        y: 304
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_73
      rect:
        serializedVersion: 2
        x: 0
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_74
      rect:
        serializedVersion: 2
        x: 16
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_75
      rect:
        serializedVersion: 2
        x: 32
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_76
      rect:
        serializedVersion: 2
        x: 64
        y: 288
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: deep_water_4
      rect:
        serializedVersion: 2
        x: 0
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: deep_water_0
      rect:
        serializedVersion: 2
        x: 16
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: deep_water_1
      rect:
        serializedVersion: 2
        x: 32
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_80
      rect:
        serializedVersion: 2
        x: 64
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_81
      rect:
        serializedVersion: 2
        x: 80
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_82
      rect:
        serializedVersion: 2
        x: 96
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_83
      rect:
        serializedVersion: 2
        x: 112
        y: 272
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: deep_water_5
      rect:
        serializedVersion: 2
        x: 0
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: deep_water_2
      rect:
        serializedVersion: 2
        x: 16
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: deep_water_3
      rect:
        serializedVersion: 2
        x: 32
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_87
      rect:
        serializedVersion: 2
        x: 64
        y: 256
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_88
      rect:
        serializedVersion: 2
        x: 0
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_89
      rect:
        serializedVersion: 2
        x: 16
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_90
      rect:
        serializedVersion: 2
        x: 32
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_91
      rect:
        serializedVersion: 2
        x: 64
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_92
      rect:
        serializedVersion: 2
        x: 80
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_93
      rect:
        serializedVersion: 2
        x: 96
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_94
      rect:
        serializedVersion: 2
        x: 112
        y: 240
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_95
      rect:
        serializedVersion: 2
        x: 0
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_96
      rect:
        serializedVersion: 2
        x: 16
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_97
      rect:
        serializedVersion: 2
        x: 32
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_98
      rect:
        serializedVersion: 2
        x: 64
        y: 224
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_99
      rect:
        serializedVersion: 2
        x: 0
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_100
      rect:
        serializedVersion: 2
        x: 16
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_101
      rect:
        serializedVersion: 2
        x: 32
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_102
      rect:
        serializedVersion: 2
        x: 64
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_103
      rect:
        serializedVersion: 2
        x: 80
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_104
      rect:
        serializedVersion: 2
        x: 96
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_105
      rect:
        serializedVersion: 2
        x: 112
        y: 208
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_106
      rect:
        serializedVersion: 2
        x: 0
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_107
      rect:
        serializedVersion: 2
        x: 16
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_108
      rect:
        serializedVersion: 2
        x: 32
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_109
      rect:
        serializedVersion: 2
        x: 64
        y: 192
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_110
      rect:
        serializedVersion: 2
        x: 0
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_111
      rect:
        serializedVersion: 2
        x: 16
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_112
      rect:
        serializedVersion: 2
        x: 32
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_113
      rect:
        serializedVersion: 2
        x: 64
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_114
      rect:
        serializedVersion: 2
        x: 80
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_115
      rect:
        serializedVersion: 2
        x: 96
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_116
      rect:
        serializedVersion: 2
        x: 112
        y: 176
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_117
      rect:
        serializedVersion: 2
        x: 0
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_118
      rect:
        serializedVersion: 2
        x: 16
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_119
      rect:
        serializedVersion: 2
        x: 32
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_120
      rect:
        serializedVersion: 2
        x: 64
        y: 160
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_121
      rect:
        serializedVersion: 2
        x: 0
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_122
      rect:
        serializedVersion: 2
        x: 16
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_123
      rect:
        serializedVersion: 2
        x: 32
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_124
      rect:
        serializedVersion: 2
        x: 64
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_125
      rect:
        serializedVersion: 2
        x: 80
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_126
      rect:
        serializedVersion: 2
        x: 96
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_127
      rect:
        serializedVersion: 2
        x: 112
        y: 144
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_128
      rect:
        serializedVersion: 2
        x: 0
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_129
      rect:
        serializedVersion: 2
        x: 16
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_130
      rect:
        serializedVersion: 2
        x: 32
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_131
      rect:
        serializedVersion: 2
        x: 64
        y: 128
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_132
      rect:
        serializedVersion: 2
        x: 0
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_133
      rect:
        serializedVersion: 2
        x: 16
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_134
      rect:
        serializedVersion: 2
        x: 32
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_135
      rect:
        serializedVersion: 2
        x: 64
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_136
      rect:
        serializedVersion: 2
        x: 80
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_137
      rect:
        serializedVersion: 2
        x: 96
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_138
      rect:
        serializedVersion: 2
        x: 112
        y: 112
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_139
      rect:
        serializedVersion: 2
        x: 0
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_140
      rect:
        serializedVersion: 2
        x: 16
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_141
      rect:
        serializedVersion: 2
        x: 32
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_142
      rect:
        serializedVersion: 2
        x: 64
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Lava_4
      rect:
        serializedVersion: 2
        x: 0
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Lava_0
      rect:
        serializedVersion: 2
        x: 16
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Lava_1
      rect:
        serializedVersion: 2
        x: 32
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_146
      rect:
        serializedVersion: 2
        x: 64
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_147
      rect:
        serializedVersion: 2
        x: 80
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_148
      rect:
        serializedVersion: 2
        x: 96
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_149
      rect:
        serializedVersion: 2
        x: 112
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Lava_5
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Lava_2
      rect:
        serializedVersion: 2
        x: 16
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Lava_3
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_153
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: water_lava_4
      rect:
        serializedVersion: 2
        x: 0
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: water_lava_0
      rect:
        serializedVersion: 2
        x: 16
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: water_lava_1
      rect:
        serializedVersion: 2
        x: 32
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_157
      rect:
        serializedVersion: 2
        x: 64
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_158
      rect:
        serializedVersion: 2
        x: 80
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_159
      rect:
        serializedVersion: 2
        x: 96
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_160
      rect:
        serializedVersion: 2
        x: 112
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: water_lava_5
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: water_lava_2
      rect:
        serializedVersion: 2
        x: 16
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: water_lava_3
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_164
      rect:
        serializedVersion: 2
        x: 64
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_165
      rect:
        serializedVersion: 2
        x: 0
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_166
      rect:
        serializedVersion: 2
        x: 16
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_167
      rect:
        serializedVersion: 2
        x: 32
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_168
      rect:
        serializedVersion: 2
        x: 64
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_169
      rect:
        serializedVersion: 2
        x: 80
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_170
      rect:
        serializedVersion: 2
        x: 96
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_171
      rect:
        serializedVersion: 2
        x: 112
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_172
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_173
      rect:
        serializedVersion: 2
        x: 16
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_174
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Pit0_175
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    outline: []
    physicsShape: []
  spritePackingTag: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
