
DAWNLIKE
16x16 Universal Rogue-like tileset v1.81

 == The Project ==

Dawnlike all started in the fall of 2013 as an idea. A crazy, ambitious idea called DawnHack. I had recently played NetHack, but was let down with the graphics. It was then that I decided I would make a tileset worthy of this incredible game! And so I began. I drew a giant ant. Then a bee, and then a soldier ant. I worked at a furious pace, churning out tiles at superhuman speeds. And lo and behold...after the first week I had blazed my way through the first 120 tiles, a total 11% of the 1057 tiles of NetHack. But little did I know, I had just taken my first brave bites out of the great whale I would one day consume.

And finally, after 3 months of on-and-off work, I ate that whale. Tail and all. Several devs including a<PERSON>523 (NetHack4) and <PERSON><PERSON> (UnNetHack) found the tileset and contacted me for some expansions. I helped them out, and the Android versions of NetHack4 and UnNetHack now include DawnHack.

But I still wasn't satisfied. I lurked the web and discovered famous tilesets like Oryx's Ultimate Roguelike, Cardinal Quest, and Dungeon Crawl Stone Soup. I discovered that in the rogue-like community, there exists a high demand for high-quality, open source tilesets. Something to make rogue-likes more accessible...something to add a little pizazz...something to help newcomers discover they love rogue-likes! And so I bring you a colossal expansion of DawnHack. My time, my sweat, my magnus opus...Dawnlike.

 == The Features ==

 - 400+ Unique characters sprites including demons, humanoids, animals, elementals, etc.
 - 800+ Item sprites including books, potions, scrolls, currency, etc.
 - Basic 2-frame animation for all characters.
 - 6 Playable races with 8-16 classes each.
 - Simple menus and GUI.
 - 48 robust wall/floor tilesets.
 - Dozens of furniture, effects, traps and doors.
 - Almost-complete overworld tileset.
 - Two hand-made, monospaced 6x6 and 8x8 truetype fonts.
 - Examples of maps.
 - And much, much more to come...

 == The Price ==

Dawnlike is registered under a CC-BY 4.0 license, meaning that you are free to redistribute or edit any part of this tileset as long as you do not take the credit for it. I cannot stop you from using this tileset commercially, so feel free to sell any games using Dawnlike.

If you use this tileset you must accredit DawnBringer. That mad color-bending genius came up with the palette this entire pack lives on. Without his palette, I would probably never would have even finished DawnHack. As for crediting me, I have a unique request. Inside "Reptiles.png" you will find a Platino sprite. If you use Dawnlike, you MUST use this sprite and hide him very well! Some ideas are...

 - Try eating a long sword 10 times and he will pop up.
 - 1/1000 rabid woodchucks will spawn with his sprite.
 - Or to quote a particularly snarky user from OpenGameArt...

	"I'm thinking of doing quests where the guy asks for 20  bear asses, but he needs 200.  and you can complete the 20 and get done with it, but platino reveals himself if you get him 200.

Is this obscure enough?  what is swallowing swords I dont even.."

If you do use this tileset commercially, please consider sending a donation via <NAME_EMAIL>. I am a freelance pixel artist, so time spent on this project is money lost. Donations will greatly help solve this.


 == The Future ==

And I'm going to keep going. There's already more than enough for a full Dwarf Fortress-sized game here, but I want to keep going and push the envelope. For every tile I make, another two ideas pop into my head. The possibilities are endless. But...no number of tiles will satisfy my lust, because graphics alone aren't fun at all. It's games that are fun, so if you create anything with this tileset, even the smallest 1DRL, please show it to me so I may one day satisfy my tile lust. :)

Planned tiles (in no particular order)

cauldrons, tilable cobwebs, more recolorings, customizable player character, customizable forgotten monster, ground spears, flag for each race, rising/falling spikes, pushable blocks, stumps, crates, trapdoors, tree fruit, mugs, gore, more meats, skeletal dragons, phoenixes, maple trees, leather materials, catapults, ballistas, bird's nest, wells, ant colonies, machinery, sun/moon cycles, logs, hive walls/floor, desert tomb walls/floor, staffs, harpies, mandrills, weapon edits (fire!), berry bushes, totems, toys, quivers, backpacks, buckets, bracelets, guitars, crutches, looms, anvils, saplings, seeds, bullion, floodgates, clouds, egg clutches, pods, mosquitoes, owls, parrots, swans, quails, cages, skull and turret-like enemy, ice decor, grassy walls, mossy walls, waterskin, belts, overworld tombs/pyramids.

Feel free to create any tiles you see here and send them to <NAME_EMAIL>. I'll add them to the sprite sheet and make you a donater sprite if you'd like. The only catch is that I have a very high bar for quality for this tileset, so don't expect me to accept them the first time.

== UPDATE LIST ==

== UPDATE 1/30/2014 ==
 - Far more tiles than default UnNetHack.
 - Organized the colossal tile sheet into bite-sized minisheets.
 - Animated every single character.
 - More terrain types and robust floor tiles.

== UPDATE 2/1/2014 ==
 - More subtle floor textures.
 - Animations are now less exaggerated.
 - Animated lava and water as well as pits.
 - Added SkyDungeon, Dungeon and Cavern examples.

== UPDATE 2/2/2014 ==
 - Gryphons; playable dwarves and kobolds/lizardmen.
 - Basic animated GUI.
 - Many more walls and floors, including grass, wood, and darkened versions.
 - Super Dragonsin Saga 6x6 and 8x8 monospaced fonts.
 - Tweaked lava textures.

== UPDATE 2/2/2014 ==
 - Split up some sheets to better categorize them.
 - Dozens of furniture pieces including animated doors, windows, jars, signs, tables, carpets, torches, shelves and chairs.
 - Farmers and female civilian.
 - Mushrooms, more chests, weapons and guns.
 - Snow and desert floor and walls.
 - Normal, desolate and snowy mountains/forests.
 - 12 animated ores and swamp/evil pits.

== UPDATE 2/5/2014 ==
 - After four months of work, Dawnlike is officially released.
 - Playable orc and tall/short generic humanoids.
 - A few aquatic, farm and woodland creatures.
 - Hills, dunes and overworld paths/rivers.
 - More furniture and some foliage.
 - A few miscellaneous recolorings and more currency.
 - Updated examples.

== UPDATE 2/9/14 ==
 - Blood, slime, water and lava splatterings.
 - 20 new magic effects and 2 rope effects.
 - 3 new walls (smooth, shiny, crystalline) with accompanying pits and palettes.
 - Tons of new aquatics, humanoids, quadrapeds and other creatures.
 - Created new "Avians" category and shortened all file names.
 - A few more pieces of decor including 6 candles and 8 beds.
 - Farmland with 16 accompanying crops and food.
 - Stone walls, 4 villages, 4 castles and 4 bridges for overworld.

== UPDATE 4/2/14 ==
 - A few dozen more animal sprites, mostly from DawnFortress.
 - 16 pieces of bone decor and 3 fences.
 - A few examples of status effects and creative uses of animation.
 - 4 more GUI bubbles.
 - Cracked, vine-covered and direction-independent walls.

== UPDATE 6/18/2014 ==
 - Added 5 animated player classes and a template commissioned by Romet.
 - Decorative skeletons, fences, statue examples and cracked walls.
 
 == UPDATE 7/26/2015 ==
  - Alternate up/down stairs, spikes, floor spears, coffins, portals, hexes and the rock mole graphic from DawnHack.
 - Added the 6 missing configurations of tree tiles, courtesy congusbongus.