fileFormatVersion: 2
guid: afcb77e431c49734cb3a7274538bb8d5
timeCreated: **********
licenseType: Free
TextureImporter:
  fileIDToRecycleName:
    21300000: Effect0_0
    21300002: Effect0_1
    21300004: Effect0_2
    21300006: Effect0_3
    21300008: Effect0_4
    21300010: Effect0_5
    21300012: Effect0_6
    21300014: Effect0_7
    21300016: Effect0_8
    21300018: Effect0_9
    21300020: Effect0_10
    21300022: Effect0_11
    21300024: Effect0_12
    21300026: Effect0_13
    21300028: Effect0_14
    21300030: Effect0_15
    21300032: Effect0_16
    21300034: Effect0_17
    21300036: Effect0_18
    21300038: Effect0_19
    21300040: Effect0_20
    21300042: Effect0_21
    21300044: Effect0_22
    21300046: Effect0_23
    21300048: Effect0_24
    21300050: Effect0_25
    21300052: Effect0_26
    21300054: Effect0_27
    21300056: Effect0_28
    21300058: Effect0_29
    21300060: Effect0_30
  externalObjects: {}
  serializedVersion: 4
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 1
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: -1
    mipBias: -1
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spritePixelsToUnits: 98
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  platformSettings:
  - buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Effect0_0
      rect:
        serializedVersion: 2
        x: 0
        y: 368
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_1
      rect:
        serializedVersion: 2
        x: 47
        y: 368
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_2
      rect:
        serializedVersion: 2
        x: 0
        y: 320
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_3
      rect:
        serializedVersion: 2
        x: 47
        y: 321
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_4
      rect:
        serializedVersion: 2
        x: 0
        y: 272
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_5
      rect:
        serializedVersion: 2
        x: 47
        y: 271
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_6
      rect:
        serializedVersion: 2
        x: 0
        y: 224
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_7
      rect:
        serializedVersion: 2
        x: 0
        y: 176
        width: 48
        height: 48
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_8
      rect:
        serializedVersion: 2
        x: 0
        y: 63
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_9
      rect:
        serializedVersion: 2
        x: 16
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_10
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_11
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_12
      rect:
        serializedVersion: 2
        x: 48
        y: 63
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_13
      rect:
        serializedVersion: 2
        x: 0
        y: 47
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_14
      rect:
        serializedVersion: 2
        x: 16
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_15
      rect:
        serializedVersion: 2
        x: 96
        y: 63
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_16
      rect:
        serializedVersion: 2
        x: 112
        y: 63
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_17
      rect:
        serializedVersion: 2
        x: 112
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_18
      rect:
        serializedVersion: 2
        x: 96
        y: 47
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_19
      rect:
        serializedVersion: 2
        x: 32
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_20
      rect:
        serializedVersion: 2
        x: 16
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_21
      rect:
        serializedVersion: 2
        x: 48
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_22
      rect:
        serializedVersion: 2
        x: 64
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_23
      rect:
        serializedVersion: 2
        x: 80
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_24
      rect:
        serializedVersion: 2
        x: 96
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_25
      rect:
        serializedVersion: 2
        x: 112
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_26
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_27
      rect:
        serializedVersion: 2
        x: 48
        y: 30
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_28
      rect:
        serializedVersion: 2
        x: 16
        y: 31
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_29
      rect:
        serializedVersion: 2
        x: 32
        y: 30
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Effect0_30
      rect:
        serializedVersion: 2
        x: 64
        y: 31
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    outline: []
    physicsShape: []
  spritePackingTag: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
