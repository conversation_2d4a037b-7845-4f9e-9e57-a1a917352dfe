fileFormatVersion: 2
guid: a32fc1de8e2053049849bbef221a25a7
timeCreated: **********
licenseType: Free
TextureImporter:
  fileIDToRecycleName:
    21300000: Ground0_0
    21300002: Ground0_1
    21300004: Ground0_2
    21300006: Ground0_3
    21300008: Ground0_4
    21300010: Ground0_5
    21300012: Ground0_6
    21300014: Ground0_7
    21300016: Ground0_8
    21300018: Ground0_9
    21300020: Ground0_10
    21300022: Ground0_11
    21300024: Ground0_12
    21300026: Ground0_13
    21300028: Ground0_14
    21300030: Ground0_15
    21300032: Ground0_16
    21300034: Ground0_17
    21300036: Ground0_18
    21300038: Ground0_19
    21300040: Ground0_20
    21300042: Ground0_21
    21300044: Ground0_22
    21300046: Ground0_23
    21300048: Ground0_24
    21300050: Ground0_25
    21300052: Ground0_26
    21300054: Ground0_27
    21300056: Ground0_28
    21300058: Ground0_29
    21300060: Ground0_30
    21300062: Ground0_31
    21300064: Ground0_32
    21300066: Ground0_33
    21300068: Ground0_34
    21300070: Ground0_35
    21300072: Ground0_36
    21300074: Ground0_37
    21300076: Ground0_38
    21300078: Ground0_39
    21300080: Ground0_40
    21300082: Ground0_41
    21300084: Ground0_42
    21300086: Ground0_43
    21300088: Ground0_44
    21300090: Ground0_45
    21300092: Ground0_46
    21300094: Ground0_47
    21300096: Ground0_48
    21300098: Ground0_49
    21300100: Ground0_50
    21300102: Ground0_51
    21300104: Ground0_52
    21300106: Ground0_53
    21300108: Ground0_54
    21300110: Ground0_55
  externalObjects: {}
  serializedVersion: 4
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
  isReadable: 1
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: -1
    mipBias: -1
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spritePixelsToUnits: 95
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  platformSettings:
  - buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: iPhone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  - buildTarget: Android
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: Ground0_0
      rect:
        serializedVersion: 2
        x: 0
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_1
      rect:
        serializedVersion: 2
        x: 16
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_2
      rect:
        serializedVersion: 2
        x: 32
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_3
      rect:
        serializedVersion: 2
        x: 48
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_5
      rect:
        serializedVersion: 2
        x: 80
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_7
      rect:
        serializedVersion: 2
        x: 112
        y: 96
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_8
      rect:
        serializedVersion: 2
        x: 0
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_9
      rect:
        serializedVersion: 2
        x: 16
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_12
      rect:
        serializedVersion: 2
        x: 64
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_13
      rect:
        serializedVersion: 2
        x: 80
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_14
      rect:
        serializedVersion: 2
        x: 96
        y: 80
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_16
      rect:
        serializedVersion: 2
        x: 0
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_17
      rect:
        serializedVersion: 2
        x: 16
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_18
      rect:
        serializedVersion: 2
        x: 32
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_19
      rect:
        serializedVersion: 2
        x: 48
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_20
      rect:
        serializedVersion: 2
        x: 64
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_21
      rect:
        serializedVersion: 2
        x: 80
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_22
      rect:
        serializedVersion: 2
        x: 96
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_23
      rect:
        serializedVersion: 2
        x: 112
        y: 64
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_24
      rect:
        serializedVersion: 2
        x: 0
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_25
      rect:
        serializedVersion: 2
        x: 16
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_26
      rect:
        serializedVersion: 2
        x: 32
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_27
      rect:
        serializedVersion: 2
        x: 48
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_28
      rect:
        serializedVersion: 2
        x: 64
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_29
      rect:
        serializedVersion: 2
        x: 80
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_30
      rect:
        serializedVersion: 2
        x: 96
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_31
      rect:
        serializedVersion: 2
        x: 112
        y: 48
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_32
      rect:
        serializedVersion: 2
        x: 0
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_34
      rect:
        serializedVersion: 2
        x: 32
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_35
      rect:
        serializedVersion: 2
        x: 48
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_36
      rect:
        serializedVersion: 2
        x: 64
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_37
      rect:
        serializedVersion: 2
        x: 80
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_38
      rect:
        serializedVersion: 2
        x: 96
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_39
      rect:
        serializedVersion: 2
        x: 112
        y: 32
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_41
      rect:
        serializedVersion: 2
        x: 16
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_43
      rect:
        serializedVersion: 2
        x: 48
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_44
      rect:
        serializedVersion: 2
        x: 64
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_45
      rect:
        serializedVersion: 2
        x: 80
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_46
      rect:
        serializedVersion: 2
        x: 96
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_47
      rect:
        serializedVersion: 2
        x: 112
        y: 16
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_48
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_50
      rect:
        serializedVersion: 2
        x: 32
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_52
      rect:
        serializedVersion: 2
        x: 64
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    - serializedVersion: 2
      name: Ground0_55
      rect:
        serializedVersion: 2
        x: 112
        y: 0
        width: 16
        height: 16
      alignment: 0
      pivot: {x: 0.5, y: 0.5}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
    outline: []
    physicsShape: []
  spritePackingTag: 
  userData: 
  assetBundleName: 
  assetBundleVariant: 
